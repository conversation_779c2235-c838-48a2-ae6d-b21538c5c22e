import { parseUnits } from 'viem';
import { BridgeConfig, getBridgeConfig, getNetworkRpcUrl, getTokenDecimals, CONTRACT_ADDRESSES } from '../utils/bridgeConfig';
import { Network } from '../components/NetworkSelector';
import { Connection, PublicKey } from '@solana/web3.js';
import { TOKEN_PROGRAM_ID } from '@solana/spl-token';
import { createUmi } from '@metaplex-foundation/umi-bundle-defaults';
import { walletAdapterIdentity } from '@metaplex-foundation/umi-signer-wallet-adapters';
import { publicKey } from '@metaplex-foundation/umi';
import { oft } from '@layerzerolabs/oft-v2-solana-sdk';
import bs58 from 'bs58';

export interface QuoteResult {
  nativeFee: string;
  receiveAmount: string;
  gasPrice?: string;
}

export interface BridgeResult {
  txHash: string;
  layerZeroScanLink?: string;
}

// Gas limits for different networks
const ETHEREUM_GAS_LIMIT = BigInt(200000);
const BASE_GAS_LIMIT = BigInt(200000);

// Utility function to convert Ethereum address to bytes32
function addressToBytes32(address: string): string {
  // Remove 0x prefix and pad to 32 bytes
  const cleanAddress = address.replace('0x', '').toLowerCase();
  return '0x' + '0'.repeat(24) + cleanAddress;
}

// Utility function to fetch gas price from a public API
async function fetchGasPrice(chainId: number = 1): Promise<{ maxFeePerGas: bigint; maxPriorityFeePerGas: bigint } | null> {
  try {
    // Use a simple fallback gas price estimation
    // In production, you might want to use services like Blocknative, Etherscan API, etc.
    const baseGasPrice = chainId === 8453 ? BigInt(1 * 1e9) : BigInt(20 * 1e9); // 1 gwei for Base, 20 gwei for Ethereum
    const priorityFee = BigInt(2 * 1e9); // 2 gwei priority fee

    return {
      maxFeePerGas: baseGasPrice + priorityFee,
      maxPriorityFeePerGas: priorityFee
    };
  } catch (error) {
    console.error('Failed to fetch gas price:', error);
    return null;
  }
}

export class BridgeService {
  private config: BridgeConfig;

  constructor(fromNetwork: Network, toNetwork: Network) {
    this.config = getBridgeConfig(fromNetwork, toNetwork);
  }

  isSupported(): boolean {
    return this.config.isSupported;
  }

  getBridgeType(): string {
    return this.config.bridgeType;
  }

  async quoteBridge(
    amount: string,
    recipientAddress: string,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    solanaWallet?: any,
    ethAddress?: string
  ): Promise<QuoteResult> {
    if (!this.config.isSupported) {
      throw new Error(`Bridge from ${this.config.fromNetwork.name} to ${this.config.toNetwork.name} is not supported yet`);
    }

    switch (this.config.bridgeType) {
      case 'SOLANA_TO_EVM':
        return this.quoteSolanaToEvm(amount, recipientAddress, solanaWallet);
      case 'EVM_TO_SOLANA':
        return this.quoteEvmToSolana(amount, recipientAddress, ethAddress || '');
      case 'EVM_TO_EVM':
        return this.quoteEvmToEvm(amount, recipientAddress, ethAddress || '');
      default:
        throw new Error('Unsupported bridge type');
    }
  }

  async executeBridge(
    amount: string,
    recipientAddress: string,
    nativeFee: string,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    solanaWallet?: any,
    ethAddress?: string,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    writeContract?: any
  ): Promise<BridgeResult> {
    if (!this.config.isSupported) {
      throw new Error(`Bridge from ${this.config.fromNetwork.name} to ${this.config.toNetwork.name} is not supported yet`);
    }

    switch (this.config.bridgeType) {
      case 'SOLANA_TO_EVM':
        return this.executeSolanaToEvm(amount, recipientAddress, nativeFee, solanaWallet);
      case 'EVM_TO_SOLANA':
        return this.executeEvmToSolana(amount, recipientAddress, nativeFee, ethAddress || '', writeContract);
      case 'EVM_TO_EVM':
        return this.executeEvmToEvm(amount, recipientAddress, nativeFee, ethAddress || '', writeContract);
      default:
        throw new Error('Unsupported bridge type');
    }
  }

  private async quoteSolanaToEvm(
    amount: string,
    recipientAddress: string,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    solanaWallet: any
  ): Promise<QuoteResult> {
    try {
      if (!solanaWallet?.publicKey) {
        throw new Error("Solana wallet not connected");
      }

      // Create UMI instance
      const rpcUrl = getNetworkRpcUrl('solana');
      const umi = createUmi(rpcUrl);
      umi.use(walletAdapterIdentity(solanaWallet));

      const mint = publicKey(CONTRACT_ADDRESSES.solana.mint!);
      const tokenDecimals = getTokenDecimals('solana');

      // Convert amount to proper decimals for Solana (6 decimals)
      const amountInTokens = BigInt(Math.floor(parseFloat(amount) * Math.pow(10, tokenDecimals)));

      // Convert recipient address to bytes32 format
      const recipientAddressBytes32 = addressToBytes32(recipientAddress);

      // Get the destination endpoint ID based on the target network
      const dstEid = this.config.toEndpointId;

      const { nativeFee } = await oft.quote(
        umi.rpc,
        {
          payer: publicKey(solanaWallet.publicKey.toString()),
          tokenMint: mint,
          tokenEscrow: publicKey(CONTRACT_ADDRESSES.solana.escrow!),
        },
        {
          payInLzToken: false,
          to: Buffer.from(recipientAddressBytes32.replace('0x', ''), 'hex'),
          dstEid,
          amountLd: amountInTokens,
          minAmountLd: amountInTokens,
          options: Buffer.from(""),
          composeMsg: undefined,
        },
        {
          oft: publicKey(CONTRACT_ADDRESSES.solana.program!),
        }
      );

      // Calculate receive amount (assuming 1:1 ratio, but with different decimals)
      const receiveAmount = parseFloat(amount).toFixed(6);

      return {
        nativeFee: nativeFee.toString(),
        receiveAmount,
      };
    } catch (error) {
      console.error('Error getting Solana to EVM quote:', error);
      // Fallback to mock quote if real quote fails
      const mockFee = "0.001"; // SOL
      const receiveAmount = parseFloat(amount).toFixed(6);

      return {
        nativeFee: parseUnits(mockFee, 9).toString(), // SOL has 9 decimals
        receiveAmount,
      };
    }
  }

  private async quoteEvmToSolana(
    amount: string,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _recipientAddress: string,
    ethAddress: string
  ): Promise<QuoteResult> {
    try {
      if (!ethAddress) {
        throw new Error("Ethereum wallet not connected");
      }

      // Get current gas prices
      const chainId = this.config.fromNetwork.id === 'ethereum' ? 1 : 8453; // Base chain ID
      const gasPrice = await fetchGasPrice(chainId);
      const gasLimit = chainId === 1 ? ETHEREUM_GAS_LIMIT : BASE_GAS_LIMIT;

      // Calculate gas cost: gasLimit * maxFeePerGas
      let nativeFee: bigint;
      if (gasPrice) {
        nativeFee = gasLimit * gasPrice.maxFeePerGas;
      } else {
        // Fallback to a reasonable estimate if gas price fetch fails
        const fallbackGasPrice = chainId === 8453 ? BigInt(2 * 1e9) : BigInt(25 * 1e9); // 2 gwei for Base, 25 gwei for Ethereum
        nativeFee = gasLimit * fallbackGasPrice;
      }

      // Calculate receive amount (assuming 1:1 ratio, but with different decimals)
      // EVM has 18 decimals, Solana has 6 decimals
      const receiveAmount = parseFloat(amount).toFixed(6);

      return {
        nativeFee: nativeFee.toString(),
        receiveAmount,
        gasPrice: gasPrice?.maxFeePerGas.toString() || (chainId === 8453 ? "2000000000" : "25000000000"),
      };
    } catch (error) {
      console.error('Error getting EVM to Solana quote:', error);
      // Fallback to mock quote if real quote fails
      const mockFee = "0.001"; // ETH
      const receiveAmount = parseFloat(amount).toFixed(6);

      return {
        nativeFee: parseUnits(mockFee, 18).toString(),
        receiveAmount,
        gasPrice: "20000000000", // 20 gwei
      };
    }
  }

  private async quoteEvmToEvm(
    amount: string,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _recipientAddress: string,
    ethAddress: string
  ): Promise<QuoteResult> {
    try {
      if (!ethAddress) {
        throw new Error("Ethereum wallet not connected");
      }

      // Get current gas prices for the source network
      const chainId = this.config.fromNetwork.id === 'ethereum' ? 1 : 8453; // Base chain ID
      const gasPrice = await fetchGasPrice(chainId);
      const gasLimit = chainId === 1 ? ETHEREUM_GAS_LIMIT : BASE_GAS_LIMIT;

      // Calculate gas cost: gasLimit * maxFeePerGas
      let nativeFee: bigint;
      if (gasPrice) {
        nativeFee = gasLimit * gasPrice.maxFeePerGas;
      } else {
        // Fallback to a reasonable estimate if gas price fetch fails
        const fallbackGasPrice = chainId === 8453 ? BigInt(2 * 1e9) : BigInt(25 * 1e9); // 2 gwei for Base, 25 gwei for Ethereum
        nativeFee = gasLimit * fallbackGasPrice;
      }

      // Calculate receive amount (assuming 1:1 ratio with same decimals)
      // Both EVM networks use 18 decimals
      const receiveAmount = parseFloat(amount).toFixed(6);

      return {
        nativeFee: nativeFee.toString(),
        receiveAmount,
        gasPrice: gasPrice?.maxFeePerGas.toString() || (chainId === 8453 ? "2000000000" : "25000000000"),
      };
    } catch (error) {
      console.error('Error getting EVM to EVM quote:', error);
      // Fallback to mock quote if real quote fails
      const mockFee = "0.001"; // ETH
      const receiveAmount = parseFloat(amount).toFixed(6);

      return {
        nativeFee: parseUnits(mockFee, 18).toString(),
        receiveAmount,
        gasPrice: "25000000000", // 25 gwei
      };
    }
  }

  private async executeSolanaToEvm(
    amount: string,
    recipientAddress: string,
    nativeFee: string,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    solanaWallet: any
  ): Promise<BridgeResult> {
    try {
      if (!solanaWallet?.publicKey) {
        throw new Error("Solana wallet not connected");
      }

      // Create UMI instance
      const rpcUrl = getNetworkRpcUrl('solana');
      const umi = createUmi(rpcUrl);
      umi.use(walletAdapterIdentity(solanaWallet));

      // Create connection for transaction handling
      const connection = new Connection(rpcUrl);

      const mint = publicKey(CONTRACT_ADDRESSES.solana.mint!);
      const tokenDecimals = getTokenDecimals('solana');

      // Convert amount to proper decimals for Solana (6 decimals)
      const amountInTokens = BigInt(Math.floor(parseFloat(amount) * Math.pow(10, tokenDecimals)));

      // Convert recipient address to bytes32 format
      const recipientAddressBytes32 = addressToBytes32(recipientAddress);

      // Get the destination endpoint ID based on the target network
      const dstEid = this.config.toEndpointId;

      // Get token account using the correct import
      const { findAssociatedTokenPda } = await import('@metaplex-foundation/mpl-toolbox');
      const { fromWeb3JsPublicKey } = await import('@metaplex-foundation/umi-web3js-adapters');

      const tokenAccount = findAssociatedTokenPda(umi, {
        mint: fromWeb3JsPublicKey(new PublicKey(CONTRACT_ADDRESSES.solana.mint!)),
        owner: publicKey(solanaWallet.publicKey.toString()),
        tokenProgramId: fromWeb3JsPublicKey(TOKEN_PROGRAM_ID),
      });

      const ix = await oft.send(
        umi.rpc,
        {
          payer: umi.identity,
          tokenMint: mint,
          tokenEscrow: publicKey(CONTRACT_ADDRESSES.solana.escrow!),
          tokenSource: tokenAccount[0],
        },
        {
          to: Buffer.from(recipientAddressBytes32.replace('0x', ''), 'hex'),
          dstEid,
          amountLd: amountInTokens,
          minAmountLd: amountInTokens,
          options: Buffer.from(""),
          composeMsg: undefined,
          nativeFee: BigInt(nativeFee),
        },
        {
          oft: publicKey(CONTRACT_ADDRESSES.solana.program!),
          token: fromWeb3JsPublicKey(TOKEN_PROGRAM_ID)
        }
      );

      // Add compute unit instructions to handle the LayerZero transaction
      const { setComputeUnitLimit, setComputeUnitPrice } = await import('@metaplex-foundation/mpl-toolbox');
      const { transactionBuilder } = await import('@metaplex-foundation/umi');
      const { toWeb3JsTransaction } = await import('@metaplex-foundation/umi-web3js-adapters');

      const computeUnitLimitIx = setComputeUnitLimit(umi, { units: 400_000 });
      const computeUnitPriceIx = setComputeUnitPrice(umi, { microLamports: 1000 });

      const {
        context: { slot: minContextSlot },
        value: { blockhash, lastValidBlockHeight }
      } = await connection.getLatestBlockhashAndContext('finalized');

      const txB = transactionBuilder()
        .add(computeUnitLimitIx)
        .add(computeUnitPriceIx)
        .add([ix])
        .setBlockhash(blockhash);

      const umiTx = txB.build(umi);
      const web3Tx = toWeb3JsTransaction(umiTx);

      const signature = await solanaWallet.sendTransaction(web3Tx, connection, { minContextSlot });
      const txHash = signature;

      await connection.confirmTransaction({ blockhash, lastValidBlockHeight, signature });

      const layerZeroScanLink = `https://layerzeroscan.com/tx/${txHash}`;

      return {
        txHash,
        layerZeroScanLink,
      };
    } catch (error) {
      console.error('Error executing Solana to EVM bridge:', error);
      throw new Error(error instanceof Error ? error.message : "Solana to EVM bridging failed");
    }
  }

  private async executeEvmToSolana(
    amount: string,
    recipientAddress: string,
    nativeFee: string,
    ethAddress: string,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    writeContract: any
  ): Promise<BridgeResult> {
    try {
      if (!ethAddress) {
        throw new Error("Ethereum wallet not connected");
      }

      if (!writeContract) {
        throw new Error("Write contract function not available");
      }

      // Import the OFT ABI
      const { oftAbi } = await import('../utils/oftAbi');

      // Convert amount to proper decimals for EVM (18 decimals)
      const tokenDecimals = getTokenDecimals(this.config.fromNetwork.id);
      const amountInTokens = parseUnits(amount, tokenDecimals);

      // Convert Solana address to bytes32 format
      const solanaAddressBytes = bs58.decode(recipientAddress);
      const recipientAddressBytes32 = `0x${Buffer.from(solanaAddressBytes).toString('hex').padStart(64, '0')}`;

      const sendParam = {
        dstEid: this.config.toEndpointId,
        to: recipientAddressBytes32 as `0x${string}`,
        amountLD: amountInTokens,
        minAmountLD: amountInTokens,
        extraOptions: '0x' as `0x${string}`,
        composeMsg: '0x' as `0x${string}`,
        oftCmd: '0x' as `0x${string}`,
      };

      const msgFee = {
        nativeFee: BigInt(nativeFee),
        lzTokenFee: BigInt(0),
      };

      // Get current gas prices for the transaction
      const chainId = this.config.fromNetwork.id === 'ethereum' ? 1 : 8453;
      const gasPrice = await fetchGasPrice(chainId);
      const gasLimit = chainId === 1 ? ETHEREUM_GAS_LIMIT : BASE_GAS_LIMIT;

      // Get the contract address for the source network
      const contractAddress = this.config.fromContractAddress;
      if (!contractAddress) {
        throw new Error(`Contract address not found for ${this.config.fromNetwork.name}`);
      }

      // Execute the actual contract transaction with optimized gas
      const txHash = await writeContract({
        address: contractAddress as `0x${string}`,
        abi: oftAbi,
        functionName: 'send',
        args: [sendParam, msgFee, ethAddress as `0x${string}`],
        value: BigInt(nativeFee),
        gas: gasLimit,
        ...(gasPrice && {
          maxFeePerGas: gasPrice.maxFeePerGas,
          maxPriorityFeePerGas: gasPrice.maxPriorityFeePerGas,
        }),
      });

      const layerZeroScanLink = `https://layerzeroscan.com/tx/${txHash}`;

      return {
        txHash,
        layerZeroScanLink,
      };
    } catch (error) {
      console.error('Error executing EVM to Solana bridge:', error);
      throw new Error(error instanceof Error ? error.message : "EVM to Solana bridging failed");
    }
  }

  private async executeEvmToEvm(
    amount: string,
    recipientAddress: string,
    nativeFee: string,
    ethAddress: string,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    writeContract: any
  ): Promise<BridgeResult> {
    try {
      if (!ethAddress) {
        throw new Error("Ethereum wallet not connected");
      }

      if (!writeContract) {
        throw new Error("Write contract function not available");
      }

      // Import the OFT ABI
      const { oftAbi } = await import('../utils/oftAbi');

      // Convert amount to proper decimals for EVM (18 decimals)
      const tokenDecimals = getTokenDecimals(this.config.fromNetwork.id);
      const amountInTokens = parseUnits(amount, tokenDecimals);

      // Convert recipient address to bytes32 format
      const recipientAddressBytes32 = addressToBytes32(recipientAddress);

      const sendParam = {
        dstEid: this.config.toEndpointId,
        to: recipientAddressBytes32 as `0x${string}`,
        amountLD: amountInTokens,
        minAmountLD: amountInTokens,
        extraOptions: '0x' as `0x${string}`,
        composeMsg: '0x' as `0x${string}`,
        oftCmd: '0x' as `0x${string}`,
      };

      const msgFee = {
        nativeFee: BigInt(nativeFee),
        lzTokenFee: BigInt(0),
      };

      // Get current gas prices for the transaction
      const chainId = this.config.fromNetwork.id === 'ethereum' ? 1 : 8453;
      const gasPrice = await fetchGasPrice(chainId);
      const gasLimit = chainId === 1 ? ETHEREUM_GAS_LIMIT : BASE_GAS_LIMIT;

      // Get the contract address for the source network
      const contractAddress = this.config.fromContractAddress;
      if (!contractAddress) {
        throw new Error(`Contract address not found for ${this.config.fromNetwork.name}`);
      }

      // Execute the actual contract transaction with optimized gas
      const txHash = await writeContract({
        address: contractAddress as `0x${string}`,
        abi: oftAbi,
        functionName: 'send',
        args: [sendParam, msgFee, ethAddress as `0x${string}`],
        value: BigInt(nativeFee),
        gas: gasLimit,
        ...(gasPrice && {
          maxFeePerGas: gasPrice.maxFeePerGas,
          maxPriorityFeePerGas: gasPrice.maxPriorityFeePerGas,
        }),
      });

      const layerZeroScanLink = `https://layerzeroscan.com/tx/${txHash}`;

      return {
        txHash,
        layerZeroScanLink,
      };
    } catch (error) {
      console.error('Error executing EVM to EVM bridge:', error);
      throw new Error(error instanceof Error ? error.message : "EVM to EVM bridging failed");
    }
  }
}
